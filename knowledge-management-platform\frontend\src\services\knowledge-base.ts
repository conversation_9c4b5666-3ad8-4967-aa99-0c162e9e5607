import api, { PaginatedResponse, PaginationParams } from './api'

export interface KnowledgeBase {
  id: string
  name: string
  description: string
  ragflow_kb_id: string
  chunk_method: string
  parser_config: any
  document_count: number
  chunk_count: number
  status: string
  created_at: string
  updated_at: string
}

export interface CreateKnowledgeBaseParams {
  name: string
  description?: string
  chunk_method?: string
  parser_config?: any
}

export interface UpdateKnowledgeBaseParams {
  name?: string
  description?: string
  chunk_method?: string
  parser_config?: any
  status?: string
}

export interface KnowledgeBaseListParams extends PaginationParams {
  status?: string
}

class KnowledgeBaseService {
  // 获取知识库列表
  async list(params: KnowledgeBaseListParams = {}) {
    const response = await api.get<PaginatedResponse<KnowledgeBase>>('/knowledge-bases', {
      params
    })
    return response.data
  }

  // 创建知识库
  async create(params: CreateKnowledgeBaseParams) {
    const response = await api.post<KnowledgeBase>('/knowledge-bases', params)
    return response.data
  }

  // 获取知识库详情
  async get(id: string) {
    const response = await api.get<KnowledgeBase>(`/knowledge-bases/${id}`)
    return response.data
  }

  // 更新知识库
  async update(id: string, params: UpdateKnowledgeBaseParams) {
    const response = await api.put<KnowledgeBase>(`/knowledge-bases/${id}`, params)
    return response.data
  }

  // 删除知识库
  async delete(id: string) {
    const response = await api.delete(`/knowledge-bases/${id}`)
    return response.data
  }

  // 同步知识库
  async sync(id: string) {
    const response = await api.post<KnowledgeBase>(`/knowledge-bases/${id}/sync`)
    return response.data
  }

  // 获取知识库统计
  async getStatistics(id: string) {
    const response = await api.get(`/knowledge-bases/${id}/statistics`)
    return response.data
  }
}

export const knowledgeBaseService = new KnowledgeBaseService()
