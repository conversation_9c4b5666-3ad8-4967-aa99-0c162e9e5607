// 工具函数

// 格式化日期
function formatDate(dateString, format = 'YYYY-MM-DD HH:mm:ss') {
    if (!dateString) return '-'
    const date = new Date(dateString)
    
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    const hours = String(date.getHours()).padStart(2, '0')
    const minutes = String(date.getMinutes()).padStart(2, '0')
    const seconds = String(date.getSeconds()).padStart(2, '0')
    
    return format
        .replace('YYYY', year)
        .replace('MM', month)
        .replace('DD', day)
        .replace('HH', hours)
        .replace('mm', minutes)
        .replace('ss', seconds)
}

// 格式化文件大小
function formatFileSize(bytes) {
    if (!bytes) return '0 B'
    
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(1024))
    
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i]
}

// 防抖函数
function debounce(func, wait) {
    let timeout
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout)
            func(...args)
        }
        clearTimeout(timeout)
        timeout = setTimeout(later, wait)
    }
}

// 显示消息提示
function showMessage(message, type = 'info', duration = 3000) {
    const messageEl = document.createElement('div')
    messageEl.className = `message ${type}`
    messageEl.textContent = message
    
    document.body.appendChild(messageEl)
    
    setTimeout(() => {
        messageEl.style.animation = 'slideUp 0.3s ease'
        setTimeout(() => {
            document.body.removeChild(messageEl)
        }, 300)
    }, duration)
}

// 显示确认对话框
function showConfirm(message, onConfirm, onCancel) {
    const overlay = document.createElement('div')
    overlay.className = 'modal-overlay'
    
    overlay.innerHTML = `
        <div class="modal" style="width: 400px;">
            <div class="modal-header">
                <h3 class="modal-title">确认操作</h3>
            </div>
            <div class="modal-body">
                <p>${message}</p>
            </div>
            <div class="modal-footer">
                <button class="btn" id="cancel-btn">取消</button>
                <button class="btn btn-primary" id="confirm-btn">确认</button>
            </div>
        </div>
    `
    
    document.body.appendChild(overlay)
    
    // 绑定事件
    overlay.querySelector('#confirm-btn').onclick = () => {
        document.body.removeChild(overlay)
        if (onConfirm) onConfirm()
    }
    
    overlay.querySelector('#cancel-btn').onclick = () => {
        document.body.removeChild(overlay)
        if (onCancel) onCancel()
    }
    
    overlay.onclick = (e) => {
        if (e.target === overlay) {
            document.body.removeChild(overlay)
            if (onCancel) onCancel()
        }
    }
}

// 显示加载状态
function showLoading() {
    document.getElementById('loading').style.display = 'flex'
}

function hideLoading() {
    document.getElementById('loading').style.display = 'none'
}

// 生成UUID
function generateUUID() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
        const r = Math.random() * 16 | 0
        const v = c == 'x' ? r : (r & 0x3 | 0x8)
        return v.toString(16)
    })
}

// 复制到剪贴板
async function copyToClipboard(text) {
    try {
        await navigator.clipboard.writeText(text)
        showMessage('复制成功', 'success')
    } catch (err) {
        // 降级方案
        const textArea = document.createElement('textarea')
        textArea.value = text
        document.body.appendChild(textArea)
        textArea.select()
        document.execCommand('copy')
        document.body.removeChild(textArea)
        showMessage('复制成功', 'success')
    }
}

// 下载文件
function downloadFile(url, filename) {
    const link = document.createElement('a')
    link.href = url
    link.download = filename
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
}

// 验证邮箱格式
function validateEmail(email) {
    const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return re.test(email)
}

// 验证密码强度
function validatePassword(password) {
    const errors = []
    
    if (password.length < 8) {
        errors.push('密码长度至少8位')
    }
    
    if (!/[A-Z]/.test(password)) {
        errors.push('密码必须包含大写字母')
    }
    
    if (!/[a-z]/.test(password)) {
        errors.push('密码必须包含小写字母')
    }
    
    if (!/\d/.test(password)) {
        errors.push('密码必须包含数字')
    }
    
    return errors
}

// 获取状态标签HTML
function getStatusTag(status) {
    const statusMap = {
        'active': { text: '活跃', class: 'active' },
        'inactive': { text: '未激活', class: 'inactive' },
        'pending': { text: '待处理', class: 'warning' },
        'processing': { text: '处理中', class: 'warning' },
        'completed': { text: '已完成', class: 'success' },
        'failed': { text: '失败', class: 'error' },
        'banned': { text: '已禁用', class: 'error' }
    }
    
    const statusInfo = statusMap[status] || { text: status, class: 'inactive' }
    return `<span class="status-tag ${statusInfo.class}">${statusInfo.text}</span>`
}

// 转义HTML
function escapeHtml(text) {
    const div = document.createElement('div')
    div.textContent = text
    return div.innerHTML
}

// 获取查询参数
function getQueryParams() {
    const params = new URLSearchParams(window.location.search)
    const result = {}
    for (const [key, value] of params) {
        result[key] = value
    }
    return result
}

// 设置查询参数
function setQueryParams(params) {
    const url = new URL(window.location)
    Object.keys(params).forEach(key => {
        if (params[key] !== null && params[key] !== undefined && params[key] !== '') {
            url.searchParams.set(key, params[key])
        } else {
            url.searchParams.delete(key)
        }
    })
    window.history.replaceState({}, '', url)
}
