/* 基础样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html, body {
    height: 100%;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB',
        'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
    background-color: #f5f5f5;
    color: #333;
}

/* 登录页面样式 */
.login-container {
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.login-box {
    background: white;
    padding: 40px;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    width: 400px;
    max-width: 90vw;
}

.login-header {
    text-align: center;
    margin-bottom: 30px;
}

.login-header h2 {
    color: #333;
    margin-bottom: 8px;
    font-size: 24px;
    font-weight: 600;
}

.login-header p {
    color: #666;
    font-size: 14px;
}

.login-form .form-group {
    margin-bottom: 20px;
}

.login-form label {
    display: block;
    margin-bottom: 6px;
    color: #333;
    font-weight: 500;
}

.login-form input {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.3s;
}

.login-form input:focus {
    outline: none;
    border-color: #1890ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 主应用样式 */
.app-container {
    height: 100vh;
    display: flex;
    flex-direction: column;
}

.app-header {
    background: white;
    height: 64px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    z-index: 10;
}

.header-left h1 {
    font-size: 20px;
    font-weight: 600;
    color: #333;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 16px;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 12px;
}

.user-dropdown {
    position: relative;
}

.btn-icon {
    background: none;
    border: none;
    font-size: 20px;
    color: #666;
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    transition: background-color 0.3s;
}

.btn-icon:hover {
    background-color: #f5f5f5;
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    min-width: 120px;
    display: none;
    z-index: 1000;
}

.dropdown-menu.show {
    display: block;
}

.dropdown-menu a {
    display: block;
    padding: 12px 16px;
    color: #333;
    text-decoration: none;
    transition: background-color 0.3s;
}

.dropdown-menu a:hover {
    background-color: #f5f5f5;
}

.app-body {
    flex: 1;
    display: flex;
    overflow: hidden;
}

/* 侧边栏样式 */
.app-sidebar {
    width: 240px;
    background: white;
    box-shadow: 2px 0 8px rgba(0, 0, 0, 0.06);
    overflow-y: auto;
}

.sidebar-nav {
    padding: 16px 0;
}

.nav-menu {
    list-style: none;
}

.nav-item {
    margin-bottom: 4px;
}

.nav-link {
    display: flex;
    align-items: center;
    padding: 12px 24px;
    color: #666;
    text-decoration: none;
    transition: all 0.3s;
    border-right: 3px solid transparent;
}

.nav-link:hover {
    background-color: #f5f5f5;
    color: #1890ff;
}

.nav-link.active {
    background-color: #e6f7ff;
    color: #1890ff;
    border-right-color: #1890ff;
}

.nav-link i {
    margin-right: 12px;
    width: 16px;
    text-align: center;
}

/* 主内容区样式 */
.app-main {
    flex: 1;
    padding: 24px;
    overflow-y: auto;
    background-color: #f5f5f5;
}

/* 页面头部 */
.page-header {
    margin-bottom: 24px;
}

.page-title {
    font-size: 20px;
    font-weight: 600;
    color: #262626;
    margin-bottom: 8px;
}

.page-description {
    color: #8c8c8c;
    font-size: 14px;
}

/* 卡片样式 */
.card {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    overflow: hidden;
}

.card-header {
    padding: 16px 24px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.card-title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
}

.card-body {
    padding: 24px;
}

/* 工具栏样式 */
.toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    flex-wrap: wrap;
    gap: 12px;
}

.toolbar-left {
    display: flex;
    gap: 12px;
    align-items: center;
}

.toolbar-right {
    display: flex;
    gap: 12px;
    align-items: center;
}

/* 按钮样式 */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 8px 16px;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    background: white;
    color: #333;
    text-decoration: none;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s;
    gap: 6px;
}

.btn:hover {
    border-color: #1890ff;
    color: #1890ff;
}

.btn-primary {
    background: #1890ff;
    border-color: #1890ff;
    color: white;
}

.btn-primary:hover {
    background: #40a9ff;
    border-color: #40a9ff;
    color: white;
}

.btn-danger {
    background: #ff4d4f;
    border-color: #ff4d4f;
    color: white;
}

.btn-danger:hover {
    background: #ff7875;
    border-color: #ff7875;
    color: white;
}

.btn-success {
    background: #52c41a;
    border-color: #52c41a;
    color: white;
}

.btn-success:hover {
    background: #73d13d;
    border-color: #73d13d;
    color: white;
}

.btn-block {
    width: 100%;
}

.btn-sm {
    padding: 4px 8px;
    font-size: 12px;
}

/* 表单样式 */
.form-group {
    margin-bottom: 16px;
}

.form-group label {
    display: block;
    margin-bottom: 6px;
    color: #333;
    font-weight: 500;
}

.form-control {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.3s;
}

.form-control:focus {
    outline: none;
    border-color: #1890ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.form-control.error {
    border-color: #ff4d4f;
}

.form-error {
    color: #ff4d4f;
    font-size: 12px;
    margin-top: 4px;
}

/* 搜索框样式 */
.search-box {
    position: relative;
    width: 300px;
}

.search-box input {
    padding-right: 40px;
}

.search-box .search-icon {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: #999;
}

/* 状态标签 */
.status-tag {
    display: inline-block;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.status-tag.active {
    background: #f6ffed;
    color: #52c41a;
    border: 1px solid #b7eb8f;
}

.status-tag.inactive {
    background: #fff2e8;
    color: #fa8c16;
    border: 1px solid #ffd591;
}

.status-tag.error {
    background: #fff2f0;
    color: #ff4d4f;
    border: 1px solid #ffccc7;
}

/* 加载样式 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.loading-spinner {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;
    color: #1890ff;
}

.loading-spinner i {
    font-size: 24px;
}

/* 隐藏类 */
.hidden {
    display: none !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .app-sidebar {
        width: 200px;
    }
    
    .app-main {
        padding: 16px;
    }
    
    .toolbar {
        flex-direction: column;
        align-items: stretch;
    }
    
    .search-box {
        width: 100%;
    }
}
