import axios, { AxiosResponse } from 'axios'
import { message } from 'antd'
import { useAuthStore } from '@/stores/auth'

// 创建axios实例
const api = axios.create({
  baseURL: '/api',
  timeout: 30000,
})

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    const { token } = useAuthStore.getState()
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  (response: AxiosResponse) => {
    const { data } = response
    
    // 如果是成功响应，直接返回数据
    if (data.success) {
      return data
    }
    
    // 如果是失败响应，显示错误信息
    message.error(data.message || '请求失败')
    return Promise.reject(new Error(data.message || '请求失败'))
  },
  (error) => {
    // 处理HTTP错误
    if (error.response) {
      const { status, data } = error.response
      
      switch (status) {
        case 401:
          message.error('登录已过期，请重新登录')
          useAuthStore.getState().logout()
          window.location.href = '/login'
          break
        case 403:
          message.error('没有权限访问')
          break
        case 404:
          message.error('请求的资源不存在')
          break
        case 500:
          message.error('服务器内部错误')
          break
        default:
          message.error(data?.message || '请求失败')
      }
    } else if (error.request) {
      message.error('网络连接失败')
    } else {
      message.error('请求配置错误')
    }
    
    return Promise.reject(error)
  }
)

export default api

// 通用API响应类型
export interface ApiResponse<T = any> {
  success: boolean
  code: number
  message: string
  data?: T
}

// 分页响应类型
export interface PaginatedResponse<T = any> {
  items: T[]
  pagination: {
    total: number
    page: number
    per_page: number
    pages: number
    has_prev: boolean
    has_next: boolean
  }
}

// 通用分页参数
export interface PaginationParams {
  page?: number
  per_page?: number
  search?: string
}
