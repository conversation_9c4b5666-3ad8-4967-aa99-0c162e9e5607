# 知识库管理平台

基于 RAGFlow 的知识库管理平台，提供企业级的知识库管理、聊天助手和敏感词管理功能。

## 项目结构

```
knowledge-management-platform/
├── backend/                    # 后端API服务
│   ├── app/                   # 应用主目录
│   │   ├── __init__.py
│   │   ├── models/            # 数据库模型
│   │   ├── services/          # 业务逻辑服务
│   │   ├── api/               # API路由
│   │   └── utils/             # 工具函数
│   ├── config/                # 配置文件
│   ├── requirements.txt       # Python依赖
│   └── run.py                # 启动文件
├── frontend/                  # 前端管理界面
│   ├── public/
│   ├── src/
│   │   ├── components/        # 组件
│   │   ├── pages/            # 页面
│   │   ├── services/         # API服务
│   │   └── utils/            # 工具函数
│   ├── package.json
│   └── index.html
├── docker/                    # Docker配置
├── docs/                      # 文档
└── README.md
```

## 功能模块

### 1. 知识库管理模块
- 知识库的创建、删除、查询
- 文档上传和管理
- 知识库配置管理

### 2. 聊天助手模块
- 助手创建和配置
- 对话管理
- 助手性能监控

### 3. 敏感词管理模块
- 敏感词词库管理
- 过滤规则配置
- 敏感词检测

## RAGFlow 连接配置

- 服务器地址：http://**************:180
- API KEY：ragflow-Q4ZjgwOTBhNzI2MTExZjBiZjBiMDI0Mm
- 参考文档：https://ragflow.com.cn/docs/dev/python_api_reference

## 快速开始

### 后端启动
```bash
cd backend
pip install -r requirements.txt
python run.py
```

### 前端启动
```bash
cd frontend
npm install
npm start
```

## 技术栈

### 后端
- Python Flask
- SQLAlchemy
- RAGFlow Python SDK

### 前端
- React
- Ant Design
- TypeScript

## 开发计划

- [x] 项目基础结构
- [ ] 后端API框架
- [ ] 知识库管理模块
- [ ] 聊天助手模块
- [ ] 敏感词管理模块
- [ ] 前端管理界面
