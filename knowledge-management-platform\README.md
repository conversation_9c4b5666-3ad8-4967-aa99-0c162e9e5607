# 知识库管理平台

基于 RAGFlow 的知识库管理平台，提供企业级的知识库管理、聊天助手和敏感词管理功能。

![平台截图](docs/images/dashboard.png)

## ✨ 特性

- 🗂️ **知识库管理** - 完整的知识库生命周期管理
- 🤖 **聊天助手** - 基于知识库的智能对话助手
- 🛡️ **敏感词过滤** - 灵活的内容安全管理
- 👥 **用户管理** - 多用户权限控制
- 🔌 **RAGFlow集成** - 无缝对接RAGFlow服务
- 📱 **响应式设计** - 支持多设备访问

## 🏗️ 项目架构

```
knowledge-management-platform/
├── backend/                    # 后端API服务 (Python Flask)
│   ├── app/
│   │   ├── models/            # 数据库模型
│   │   ├── api/               # API路由
│   │   ├── utils/             # 工具函数
│   │   └── __init__.py
│   ├── config/                # 配置文件
│   ├── requirements.txt
│   └── run.py
├── frontend/                  # 前端界面 (HTML+CSS+JS)
│   ├── css/                   # 样式文件
│   ├── js/                    # JavaScript文件
│   │   ├── pages/            # 页面逻辑
│   │   ├── components.js     # 通用组件
│   │   ├── api.js            # API服务
│   │   └── app.js            # 主应用
│   └── index.html
├── docker/                    # Docker部署配置
├── scripts/                   # 启动脚本
├── docs/                      # 项目文档
└── README.md
```

## 🚀 快速开始

### 方式一：Docker部署（推荐）

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd knowledge-management-platform
   ```

2. **配置环境**
   ```bash
   cp docker/.env.example docker/.env
   # 编辑 .env 文件配置参数
   ```

3. **启动服务**

   **Linux/macOS:**
   ```bash
   chmod +x scripts/start.sh
   ./scripts/start.sh
   ```

   **Windows:**
   ```cmd
   scripts\start.bat
   ```

4. **访问系统**
   - 管理界面: http://localhost:3000
   - 默认账户: admin / admin123456

### 方式二：本地开发

1. **后端启动**
   ```bash
   cd backend
   pip install -r requirements.txt
   python run.py
   ```

2. **前端启动**
   ```bash
   cd frontend
   # 使用任意HTTP服务器，如：
   python -m http.server 3000
   # 或使用live-server
   npm install -g live-server
   live-server --port=3000
   ```

## 📋 功能模块

### 🗂️ 知识库管理
- ✅ 知识库创建、编辑、删除
- ✅ 文档上传和解析
- ✅ 分块方法配置
- ✅ 解析状态监控
- ✅ 统计信息查看

### 🤖 聊天助手
- ✅ 助手创建和配置
- ✅ 知识库关联
- ✅ 系统提示词设置
- ✅ 对话测试功能
- ✅ 助手性能统计

### 🛡️ 敏感词管理
- ✅ 敏感词增删改查
- ✅ 批量导入功能
- ✅ 分类和级别管理
- ✅ 实时检测API
- ✅ 命中统计分析

### 👥 用户管理
- ✅ 用户账户管理
- ✅ 角色权限控制
- ✅ 密码重置功能
- ✅ 登录状态统计

## 🔧 技术栈

### 后端技术
- **Python 3.11** - 主要开发语言
- **Flask** - Web框架
- **SQLAlchemy** - ORM框架
- **MySQL** - 主数据库
- **Redis** - 缓存数据库
- **JWT** - 身份认证

### 前端技术
- **HTML5 + CSS3** - 页面结构和样式
- **JavaScript ES6+** - 交互逻辑
- **Font Awesome** - 图标库
- **响应式设计** - 多设备适配

### 部署技术
- **Docker** - 容器化部署
- **Docker Compose** - 服务编排
- **Nginx** - 反向代理
- **Gunicorn** - WSGI服务器

## 🔌 RAGFlow 集成

本平台与 RAGFlow 深度集成，提供以下能力：

- **知识库同步** - 自动同步知识库状态
- **文档解析** - 利用RAGFlow的文档解析能力
- **智能检索** - 基于向量数据库的语义检索
- **对话生成** - 结合LLM的智能问答

### 连接配置
- 服务器: `http://**************:180`
- API Key: `ragflow-Q4ZjgwOTBhNzI2MTExZjBiZjBiMDI0Mm`
- 文档: https://ragflow.com.cn/docs/dev/python_api_reference

## 📖 文档

- [API文档](docs/API.md) - 详细的API接口说明
- [部署指南](docs/DEPLOYMENT.md) - 生产环境部署指南
- [开发指南](docs/DEVELOPMENT.md) - 开发环境搭建

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📄 许可证

MIT License
