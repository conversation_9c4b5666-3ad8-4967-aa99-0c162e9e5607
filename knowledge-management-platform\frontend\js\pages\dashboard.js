// 仪表盘页面

class DashboardPage extends BasePage {
    constructor() {
        super()
        this.stats = null
    }

    async render() {
        this.showLoading()
        
        try {
            // 获取统计数据
            await this.loadStatistics()
            
            const html = `
                <div class="page-header">
                    <h1 class="page-title">仪表盘</h1>
                    <p class="page-description">系统概览和统计信息</p>
                </div>
                
                <!-- 统计卡片 -->
                <div class="stats-grid">
                    ${this.renderStatCards()}
                </div>
                
                <!-- 最近活动 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">最近活动</h3>
                        <button class="btn btn-sm" onclick="this.refreshStats()">
                            <i class="fas fa-sync-alt"></i> 刷新
                        </button>
                    </div>
                    <div class="card-body">
                        ${this.renderRecentActivity()}
                    </div>
                </div>
            `
            
            this.renderContent(html)
        } catch (error) {
            this.showError(error.message || '加载失败')
        }
    }

    async loadStatistics() {
        try {
            // 并行获取各模块统计数据
            const [kbStats, assistantStats, wordStats, userStats] = await Promise.all([
                this.getKnowledgeBaseStats(),
                this.getChatAssistantStats(),
                this.getSensitiveWordStats(),
                this.getUserStats()
            ])

            this.stats = {
                knowledgeBase: kbStats,
                chatAssistant: assistantStats,
                sensitiveWord: wordStats,
                user: userStats
            }
        } catch (error) {
            console.error('Failed to load statistics:', error)
            this.stats = {
                knowledgeBase: { total: 0, active: 0 },
                chatAssistant: { total: 0, active: 0 },
                sensitiveWord: { total: 0, active: 0 },
                user: { total: 0, active: 0 }
            }
        }
    }

    async getKnowledgeBaseStats() {
        try {
            const data = await knowledgeBaseAPI.list({ per_page: 1 })
            return {
                total: data.pagination.total,
                active: data.pagination.total // 简化统计
            }
        } catch (error) {
            return { total: 0, active: 0 }
        }
    }

    async getChatAssistantStats() {
        try {
            const data = await chatAssistantAPI.list({ per_page: 1 })
            return {
                total: data.pagination.total,
                active: data.pagination.total // 简化统计
            }
        } catch (error) {
            return { total: 0, active: 0 }
        }
    }

    async getSensitiveWordStats() {
        try {
            const data = await sensitiveWordAPI.getStatistics()
            return {
                total: data.total_words || 0,
                active: data.active_words || 0
            }
        } catch (error) {
            return { total: 0, active: 0 }
        }
    }

    async getUserStats() {
        try {
            if (authManager.isAdmin()) {
                const data = await userAPI.getStatistics()
                return {
                    total: data.total_users || 0,
                    active: data.active_users || 0
                }
            }
            return { total: 0, active: 0 }
        } catch (error) {
            return { total: 0, active: 0 }
        }
    }

    renderStatCards() {
        if (!this.stats) return ''

        const cards = [
            {
                title: '知识库总数',
                value: this.stats.knowledgeBase.total,
                icon: 'fas fa-database',
                iconClass: 'primary'
            },
            {
                title: '聊天助手',
                value: this.stats.chatAssistant.total,
                icon: 'fas fa-robot',
                iconClass: 'success'
            },
            {
                title: '敏感词库',
                value: this.stats.sensitiveWord.total,
                icon: 'fas fa-shield-alt',
                iconClass: 'warning'
            }
        ]

        // 如果是管理员，显示用户统计
        if (authManager.isAdmin()) {
            cards.push({
                title: '用户总数',
                value: this.stats.user.total,
                icon: 'fas fa-users',
                iconClass: 'danger'
            })
        }

        return cards.map(card => createStatCard(card.title, card.value, card.icon, card.iconClass)).join('')
    }

    renderRecentActivity() {
        // 这里可以显示最近的操作记录
        return `
            <div class="empty-state">
                <i class="fas fa-clock"></i>
                <h3>暂无最近活动</h3>
                <p>开始使用系统后，这里将显示最近的操作记录</p>
            </div>
        `
    }

    bindEvents() {
        // 绑定刷新按钮事件
        window.refreshStats = async () => {
            await this.render()
            showMessage('统计数据已刷新', 'success')
        }
    }
}
