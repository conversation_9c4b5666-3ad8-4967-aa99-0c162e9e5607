#!/usr/bin/env python3
import os
import sys
import logging
from app import create_app, db
# 导入所有模型以确保表被创建
from app.models import *

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/app.log'),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

def create_admin_user():
    """创建默认管理员用户"""
    admin_username = os.environ.get('ADMIN_USERNAME', 'admin')
    admin_password = os.environ.get('ADMIN_PASSWORD', 'admin123456')
    admin_email = os.environ.get('ADMIN_EMAIL', '<EMAIL>')
    
    # 检查管理员是否已存在
    admin = User.query.filter_by(username=admin_username, is_deleted=False).first()
    if not admin:
        admin = User(
            username=admin_username,
            email=admin_email,
            nickname='系统管理员',
            role='admin',
            status='active'
        )
        admin.set_password(admin_password)
        admin.save()
        logger.info(f"Created default admin user: {admin_username}")
    else:
        logger.info(f"Admin user already exists: {admin_username}")

def init_database():
    """初始化数据库"""
    try:
        # 创建所有表
        db.create_all()
        logger.info("Database tables created successfully")
        
        # 创建默认管理员
        create_admin_user()
        
    except Exception as e:
        logger.error(f"Failed to initialize database: {str(e)}")
        sys.exit(1)

if __name__ == '__main__':
    # 创建应用
    app = create_app()
    
    # 确保日志目录存在
    os.makedirs('logs', exist_ok=True)
    
    # 确保上传目录存在
    upload_folder = app.config.get('UPLOAD_FOLDER', 'uploads')
    os.makedirs(upload_folder, exist_ok=True)
    
    with app.app_context():
        # 初始化数据库
        init_database()
    
    # 获取配置
    host = os.environ.get('HOST', '0.0.0.0')
    port = int(os.environ.get('PORT', 5000))
    debug = os.environ.get('FLASK_ENV') == 'development'
    
    logger.info(f"Starting Knowledge Management Platform on {host}:{port}")
    logger.info(f"Debug mode: {debug}")
    
    # 启动应用
    app.run(host=host, port=port, debug=debug)
