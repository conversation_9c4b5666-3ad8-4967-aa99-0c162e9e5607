{"name": "knowledge-management-platform-frontend", "version": "1.0.0", "description": "知识库管理平台前端", "private": true, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint src --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint src --ext .js,.jsx,.ts,.tsx --fix"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.8.0", "antd": "^5.12.0", "@ant-design/icons": "^5.2.0", "axios": "^1.6.0", "dayjs": "^1.11.0", "zustand": "^4.4.0", "react-query": "^3.39.0", "ahooks": "^3.7.0"}, "devDependencies": {"@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "@vitejs/plugin-react": "^4.0.0", "eslint": "^8.45.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.0", "typescript": "^5.0.0", "vite": "^4.4.0", "less": "^4.2.0"}}