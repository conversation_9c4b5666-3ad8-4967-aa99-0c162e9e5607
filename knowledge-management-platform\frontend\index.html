<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>知识库管理平台</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/components.css">
    <!-- 引入图标库 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- 登录页面 -->
    <div id="login-page" class="login-container" style="display: none;">
        <div class="login-box">
            <div class="login-header">
                <h2>知识库管理平台</h2>
                <p>请登录您的账户</p>
            </div>
            <form id="login-form" class="login-form">
                <div class="form-group">
                    <label>用户名/邮箱</label>
                    <input type="text" id="username" name="username" required>
                </div>
                <div class="form-group">
                    <label>密码</label>
                    <input type="password" id="password" name="password" required>
                </div>
                <button type="submit" class="btn btn-primary btn-block">登录</button>
            </form>
        </div>
    </div>

    <!-- 主应用界面 -->
    <div id="main-app" class="app-container">
        <!-- 顶部导航 -->
        <header class="app-header">
            <div class="header-left">
                <h1>知识库管理平台</h1>
            </div>
            <div class="header-right">
                <div class="user-info">
                    <span id="user-nickname">管理员</span>
                    <div class="user-dropdown">
                        <button class="btn-icon" id="user-menu-btn">
                            <i class="fas fa-user-circle"></i>
                        </button>
                        <div class="dropdown-menu" id="user-menu">
                            <a href="#" id="profile-btn">个人资料</a>
                            <a href="#" id="logout-btn">退出登录</a>
                        </div>
                    </div>
                </div>
            </div>
        </header>

        <!-- 主体内容 -->
        <div class="app-body">
            <!-- 侧边栏 -->
            <aside class="app-sidebar">
                <nav class="sidebar-nav">
                    <ul class="nav-menu">
                        <li class="nav-item">
                            <a href="#" class="nav-link active" data-page="dashboard">
                                <i class="fas fa-tachometer-alt"></i>
                                <span>仪表盘</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="#" class="nav-link" data-page="knowledge-base">
                                <i class="fas fa-database"></i>
                                <span>知识库管理</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="#" class="nav-link" data-page="chat-assistant">
                                <i class="fas fa-robot"></i>
                                <span>聊天助手</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="#" class="nav-link" data-page="sensitive-word">
                                <i class="fas fa-shield-alt"></i>
                                <span>敏感词管理</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="#" class="nav-link" data-page="user-management">
                                <i class="fas fa-users"></i>
                                <span>用户管理</span>
                            </a>
                        </li>
                    </ul>
                </nav>
            </aside>

            <!-- 主内容区 -->
            <main class="app-main">
                <div id="page-content">
                    <!-- 页面内容将在这里动态加载 -->
                </div>
            </main>
        </div>
    </div>

    <!-- 模态框容器 -->
    <div id="modal-container"></div>

    <!-- 加载提示 -->
    <div id="loading" class="loading-overlay" style="display: none;">
        <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>

    <!-- 引入JS文件 -->
    <script src="js/utils.js"></script>
    <script src="js/api.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/components.js"></script>
    <script src="js/pages/dashboard.js"></script>
    <script src="js/pages/knowledge-base.js"></script>
    <script src="js/pages/chat-assistant.js"></script>
    <script src="js/pages/sensitive-word.js"></script>
    <script src="js/pages/user-management.js"></script>
    <script src="js/app.js"></script>
</body>
</html>
